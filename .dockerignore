# 版本控制
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.pyc
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.venv/
venv/
ENV/
env/
# 确保子目录中的虚拟环境也被排除 - 这些目录占用了大量空间
*/.venv/
*/venv/
*/ENV/
*/env/
**/.venv/
**/venv/
main/xiaozhi-server/.venv/
main/xiaozhi-server/venv/
# 明确排除大型虚拟环境目录
main/xiaozhi-server/.venv
main/xiaozhi-server/venv
*.egg-info/
dist/
build/
eggs/
parts/
sdist/
var/
wheels/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm/
.yarn/
.pnp.*

# Java/Maven
target/
.mvn/
*.class
*.jar
*.war
*.ear
*.logs
*.iml
main/manager-api/target/
main/manager-api/logs/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# Docker
.dockerignore
Dockerfile*
docker-compose*
.docker/

# 项目特定
.env
Dockerfile
tmp/
data/
logs/
*.log

# 音乐文件 - 如果不需要在Docker镜像中包含
main/xiaozhi-server/music/*.mp3

# 日志和缓存
**/logs/
**/log/
**/cache/
**/tmp/
**/__pycache__/
**/.pytest_cache/
main/xiaozhi-server/logs/
main/manager-api/logs/

# 其他可能不需要的文件
.fastRequest/
.trace/
.trae/
.github/
main/xiaozhi-server/data/
dev-tools/
.docker_cache/
.roo/