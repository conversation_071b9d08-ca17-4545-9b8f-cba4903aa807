version: '3'
services:
  xiaozhi-esp32-server:
    image: xiaozhi-esp32-server:server_latest
    container_name: xiaozhi-esp32-server
    restart: always
    environment:
      - TZ=Asia/Shanghai
    security_opt:
      - seccomp:unconfined
    ports:
      # ws服务端
      - "8100:8000"
    volumes:
      # 配置文件目录
      - ./data:/opt/xiaozhi-esp32-server/data
      # 模型文件挂接，很重要
      - ./models/SenseVoiceSmall/model.pt:/opt/xiaozhi-esp32-server/models/SenseVoiceSmall/model.pt
  xiaozhi-esp32-server-web:
    image: xiaozhi-esp32-server:web_latest
    container_name: xiaozhi-esp32-server-web
    restart: always
    ports:
      # 智控台
      - "8002:8002"
    environment:
      - TZ=Asia/Shanghai
      ##记得改mysql和redis IP 密码
      - SPRING_DATASOURCE_DRUID_URL=************************************************************************************************************************
      - SPRING_DATASOURCE_DRUID_USERNAME=root
      - SPRING_DATASOURCE_DRUID_PASSWORD=136sonic@Mysql
      - SPRING_DATA_REDIS_HOST=************
      - SPRING_DATA_REDIS_PORT=6379
    volumes:
      # 配置文件目录
      - ./uploadfile:/app/uploadfile