#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智ESP32服务器远程Docker构建工具

此脚本实现了远程Docker构建功能，类似于docker-maven-plugin，
可以直接在远程服务器上构建Docker镜像，无需在本地存储镜像文件。
"""

import os
import sys
import time
import argparse
import tarfile
import io
import json
import getpass
import socket
import shutil
from pathlib import Path
import docker
from docker.errors import DockerException, APIError, ImageNotFound
from docker.tls import TLSConfig
import colorama
from colorama import Fore, Style
import urllib3
import warnings
from dotenv import load_dotenv

# 禁用不安全请求警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
# 忽略其他常见警告
warnings.filterwarnings("ignore", message="urllib3.*or chardet.*")

# 初始化颜色支持
colorama.init()

# 加载环境变量
ENV_FILE = ".env"
ENV_TEMPLATE = ".env.template"

if os.path.exists(ENV_FILE):
    load_dotenv(ENV_FILE)
    print(f"{Fore.GREEN}[INFO] 已加载环境变量: {ENV_FILE}{Style.RESET_ALL}")
else:
    print(f"{Fore.YELLOW}[WARN] 环境配置文件 {ENV_FILE} 不存在{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[WARN] 请复制 {ENV_TEMPLATE} 为 {ENV_FILE} 并填写您的配置{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}[WARN] 将使用默认配置继续...{Style.RESET_ALL}")

# 配置常量
DEFAULT_IMAGE_PREFIX = "xiaozhi-esp32-server"
DEFAULT_IMAGE_TAG = "latest"
DOCKERFILE_SERVER = "Dockerfile-server"
DOCKERFILE_WEB = "Dockerfile-web"
EXCLUDE_PATTERNS = ['.git', 'node_modules', 'target', '__pycache__', '.idea', '.vscode']
MAX_RETRIES = 5  # 增加到5次
RETRY_DELAY = 10  # 增加到10秒

# 服务器配置
SERVERS = {
    "prod": {
        "name": "正式环境",
        "host": os.getenv("PROD_HOST", "*************"),
        "port": int(os.getenv("PROD_PORT", "2376")),
        "cert_path": os.getenv("PROD_CERT_PATH", "/Users/<USER>/.docker/certs")
    },
    "test": {
        "name": "测试环境",
        "host": os.getenv("TEST_HOST", "**************"),
        "port": int(os.getenv("TEST_PORT", "2376")),
        "cert_path": os.getenv("TEST_CERT_PATH", "/Users/<USER>/.docker/certs/234")
    }
}

# 构建参数设置
DEFAULT_BUILD_ARGS = {
    'NPM_CONFIG_REGISTRY': 'https://registry.npmmirror.com',
    'DOCKER_BUILDKIT': '1',  # 启用BuildKit以提高构建性能
}

# 默认镜像源设置 - 使用清华源
DEFAULT_PIP_MIRROR = "https://pypi.tuna.tsinghua.edu.cn/simple"  # 清华pip镜像源

# Dockerfile备份后缀
DOCKERFILE_BACKUP_SUFFIX = ".bak"

# Docker镜像加速器
DOCKER_MIRRORS = [
    "https://registry.docker-cn.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
]

# numpy版本兼容性处理
def process_requirements(file_path):
    """处理requirements.txt文件，解决版本兼容性问题"""
    if not os.path.exists(file_path):
        return False

    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()

        updated = False
        for i, line in enumerate(lines):
            # 处理numpy版本问题
            if line.strip().startswith("numpy==1.26.4"):
                lines[i] = "numpy==1.24.3\n"  # 使用更广泛支持的版本
                updated = True

        if updated:
            with open(file_path, 'w') as f:
                f.writelines(lines)
            return True
        return False
    except Exception as e:
        print(f"{Fore.RED}[ERROR] 处理requirements.txt失败: {str(e)}{Style.RESET_ALL}")
        return False

class DockerRemoteBuilder:
    """实现远程Docker构建功能的主类"""

    def __init__(self, args):
        """初始化构建器"""
        self.args = args
        self.client = None
        self.selected_server = None
        self.clean_only = args.clean_only
        self.build_server = (args.server_only or (not args.web_only and not args.clean_only))
        self.build_web = (args.web_only or (not args.server_only and not args.clean_only))
        self.image_prefix = args.prefix
        self.image_tag = args.tag
        self.server_image = f"{self.image_prefix}:server_{self.image_tag}"
        self.web_image = f"{self.image_prefix}:web_{self.image_tag}"
        self.timeout = args.timeout
        self.retries = args.retries if args.retries > 0 else MAX_RETRIES

        # 清理选项
        self.should_clean = args.clean or args.clean_only or (not args.no_clean and args.yes)
        self.force_clean = args.force_clean

        # pip镜像源设置 - 使用清华源
        self.pip_mirror = args.pip_mirror if args.pip_mirror else DEFAULT_PIP_MIRROR

        # 构建参数
        self.build_args = DEFAULT_BUILD_ARGS.copy()
        # 如果用户指定了pip源
        if args.pip_mirror:
            self.build_args['PIP_INDEX_URL'] = args.pip_mirror
        # 如果用户指定了npm源
        if args.npm_mirror:
            self.build_args['NPM_CONFIG_REGISTRY'] = args.npm_mirror

        # 如果设置了具体服务器，直接使用
        if args.prod:
            self.selected_server = "prod"
        elif args.test:
            self.selected_server = "test"

        # 记录高级构建选项
        self.print_info(f"网络模式: {'host' if args.network_host else '默认'}")
        if args.no_cache:
            self.print_info("已启用: 禁用缓存")

    def print_info(self, message):
        """打印信息日志"""
        print(f"{Fore.GREEN}[INFO] {message}{Style.RESET_ALL}")

    def print_warn(self, message):
        """打印警告日志"""
        print(f"{Fore.YELLOW}[WARN] {message}{Style.RESET_ALL}")

    def print_error(self, message):
        """打印错误日志"""
        print(f"{Fore.RED}[ERROR] {message}{Style.RESET_ALL}")

    def select_server(self):
        """交互式选择目标服务器"""
        if self.selected_server:
            return True

        print(f"{Fore.YELLOW}请选择要构建的目标服务器环境:{Style.RESET_ALL}")
        print(f"1) 正式环境 ({SERVERS['prod']['host']})")
        print(f"2) 测试环境 ({SERVERS['test']['host']})")

        try:
            selection = input("请输入选项 [2]: ").strip()
            if not selection:
                selection = "2"

            if selection == "1":
                self.selected_server = "prod"
            elif selection == "2":
                self.selected_server = "test"
            else:
                self.print_warn(f"无效选项 '{selection}'，使用默认: 测试环境")
                self.selected_server = "test"

            server_info = SERVERS[self.selected_server]
            self.print_info(f"已选择: {server_info['name']} ({server_info['host']})")
            return True
        except (KeyboardInterrupt, EOFError):
            self.print_error("操作已取消")
            return False

    def connect_to_docker(self):
        """连接到远程Docker守护进程，支持重试"""
        server_info = SERVERS[self.selected_server]
        host = server_info['host']
        port = server_info['port']
        cert_path = server_info['cert_path']

        self.print_info(f"正在连接到远程Docker守护进程: {host}:{port}")

        # 检查证书文件是否存在
        ca_cert = os.path.join(cert_path, 'ca.pem')
        client_cert = os.path.join(cert_path, 'cert.pem')
        client_key = os.path.join(cert_path, 'key.pem')

        for cert_file in [ca_cert, client_cert, client_key]:
            if not os.path.isfile(cert_file):
                self.print_error(f"证书文件不存在: {cert_file}")
                return False

        # 创建TLS配置
        tls_config = TLSConfig(
            ca_cert=ca_cert,
            client_cert=(client_cert, client_key),
            verify=True
        )

        # 尝试多次连接
        for attempt in range(1, self.retries + 1):
            try:
                base_url = f"https://{host}:{port}"
                self.client = docker.DockerClient(
                    base_url=base_url,
                    tls=tls_config,
                    timeout=self.timeout
                )

                # 测试连接
                info = self.client.info()
                self.print_info(f"成功连接到Docker守护进程: {info.get('Name', '未命名主机')}")

                # 检查Docker镜像加速器配置
                setup_docker_mirror(self.client)

                return True
            except (DockerException, socket.timeout) as e:
                if attempt < self.retries:
                    self.print_warn(f"连接尝试 {attempt}/{self.retries} 失败: {str(e)}")
                    self.print_info(f"等待 {RETRY_DELAY} 秒后重试...")
                    time.sleep(RETRY_DELAY)
                else:
                    self.print_error(f"无法连接到Docker守护进程，已尝试 {self.retries} 次: {str(e)}")
                    return False

    def backup_dockerfile(self, dockerfile_path):
        """备份Dockerfile

        Args:
            dockerfile_path: Dockerfile路径

        Returns:
            bool: 是否成功备份
        """
        try:
            backup_path = f"{dockerfile_path}{DOCKERFILE_BACKUP_SUFFIX}"
            if os.path.exists(dockerfile_path):
                shutil.copy2(dockerfile_path, backup_path)
                self.print_info(f"已备份Dockerfile: {dockerfile_path} -> {backup_path}")
                return True
            else:
                self.print_error(f"Dockerfile不存在: {dockerfile_path}")
                return False
        except Exception as e:
            self.print_error(f"备份Dockerfile失败: {str(e)}")
            return False

    def restore_dockerfile(self, dockerfile_path):
        """还原Dockerfile

        Args:
            dockerfile_path: Dockerfile路径

        Returns:
            bool: 是否成功还原
        """
        try:
            backup_path = f"{dockerfile_path}{DOCKERFILE_BACKUP_SUFFIX}"
            if os.path.exists(backup_path):
                shutil.copy2(backup_path, dockerfile_path)
                os.remove(backup_path)
                self.print_info(f"已还原Dockerfile: {backup_path} -> {dockerfile_path}")
                return True
            else:
                self.print_warn(f"Dockerfile备份不存在: {backup_path}")
                return False
        except Exception as e:
            self.print_error(f"还原Dockerfile失败: {str(e)}")
            return False

    def modify_dockerfile_for_pip_mirror(self, dockerfile_path):
        """修改Dockerfile，添加pip镜像源

        Args:
            dockerfile_path: Dockerfile路径

        Returns:
            bool: 是否成功修改
        """
        try:
            # 读取原始Dockerfile
            with open(dockerfile_path, 'r') as f:
                content = f.read()

            # 查找并替换pip install命令
            pip_install_pattern = "RUN pip install --no-cache-dir -r requirements.txt"
            pip_install_replacement = f"RUN pip install --no-cache-dir -r requirements.txt --index-url={self.pip_mirror} --timeout=300 --retries=5"

            # 检查是否包含pip install命令
            if pip_install_pattern in content:
                self.print_info(f"找到pip install命令，将替换为使用清华源的命令")
                modified_content = content.replace(
                    pip_install_pattern,
                    pip_install_replacement
                )

                # 写入修改后的Dockerfile
                with open(dockerfile_path, 'w') as f:
                    f.write(modified_content)

                self.print_info(f"已修改Dockerfile: {dockerfile_path}")

                # 显示修改后的pip install命令
                import re
                pip_install_lines = re.findall(r'RUN.*pip install.*', modified_content)
                if pip_install_lines:
                    self.print_info("修改后的pip install命令:")
                    for line in pip_install_lines:
                        self.print_info(f"  {line}")

                return True
            else:
                self.print_warn(f"未找到pip install命令，将尝试使用正则表达式匹配")
                import re
                # 使用更宽松的正则表达式匹配pip install命令
                pattern = r'(RUN\s+pip\s+install\s+.*?-r\s+requirements\.txt)'
                match = re.search(pattern, content)
                if match:
                    original_cmd = match.group(1)
                    self.print_info(f"找到pip install命令: {original_cmd}")
                    modified_content = content.replace(
                        original_cmd,
                        f"{original_cmd} --index-url={self.pip_mirror} --timeout=300 --retries=5"
                    )

                    # 写入修改后的Dockerfile
                    with open(dockerfile_path, 'w') as f:
                        f.write(modified_content)

                    self.print_info(f"已修改Dockerfile: {dockerfile_path}")
                    return True
                else:
                    self.print_warn(f"未找到pip install命令，Dockerfile保持不变")
                    return False
        except Exception as e:
            self.print_error(f"修改Dockerfile失败: {str(e)}")
            return False

    def create_build_context(self, dockerfile):
        """创建构建上下文"""
        self.print_info("准备构建上下文...")
        context_buffer = io.BytesIO()

        try:
            with tarfile.open(fileobj=context_buffer, mode='w:gz') as tar:
                # 添加dockerignore文件（如果存在）
                dockerignore_path = '.dockerignore'
                if os.path.exists(dockerignore_path):
                    tar.add(dockerignore_path)
                    self.print_info("添加 .dockerignore 文件")
                else:
                    self.print_warn("未找到 .dockerignore 文件，构建上下文可能较大")

                for root, dirs, files in os.walk('.'):
                    # 过滤不需要的目录
                    dirs[:] = [d for d in dirs if not any(pattern in d for pattern in EXCLUDE_PATTERNS)]

                    for file in files:
                        file_path = os.path.join(root, file)
                        # 跳过大的二进制文件和临时文件
                        if any(pattern in file_path for pattern in EXCLUDE_PATTERNS):
                            continue

                        # 计算存档中的路径
                        arcname = os.path.normpath(file_path)
                        if arcname.startswith('./'):
                            arcname = arcname[2:]

                        self.print_info(f"添加文件: {arcname}") if self.args.verbose else None
                        try:
                            tar.add(file_path, arcname=arcname)
                        except Exception as e:
                            self.print_warn(f"无法添加文件 {file_path}: {str(e)}")

            context_size = context_buffer.tell() / (1024 * 1024)  # 转换为MB
            context_buffer.seek(0)
            self.print_info(f"构建上下文准备完成，大小: {context_size:.2f} MB")
            return context_buffer
        except Exception as e:
            self.print_error(f"创建构建上下文失败: {str(e)}")
            return None

    def build_image(self, image_type):
        """构建指定类型的Docker镜像，支持重试"""
        if image_type == "server":
            dockerfile = DOCKERFILE_SERVER
            image_name = self.server_image

            # 处理服务器requirements.txt文件
            req_file = "main/xiaozhi-server/requirements.txt"
            if os.path.exists(req_file):
                if process_requirements(req_file):
                    self.print_info(f"已更新 {req_file} 文件，将numpy版本调整为兼容版本")
                else:
                    self.print_info(f"检查 {req_file} 文件，无需调整")

            # 显示pip镜像源信息
            self.print_info(f"pip镜像源: {self.pip_mirror} (清华源)")

            # 备份并修改Dockerfile，添加pip镜像源
            self.print_info(f"准备修改Dockerfile: {dockerfile}")
            if not self.backup_dockerfile(dockerfile):
                self.print_error(f"无法备份Dockerfile: {dockerfile}")
                return False

            if not self.modify_dockerfile_for_pip_mirror(dockerfile):
                self.print_warn(f"无法修改Dockerfile: {dockerfile}，将使用原始版本")
                # 还原Dockerfile
                self.restore_dockerfile(dockerfile)
        else:  # web
            dockerfile = DOCKERFILE_WEB
            image_name = self.web_image

            # 显示npm镜像源信息
            if 'NPM_CONFIG_REGISTRY' in self.build_args:
                self.print_info(f"npm镜像源: {self.build_args.get('NPM_CONFIG_REGISTRY')}")

        self.print_info(f"开始构建 {image_type} 镜像: {image_name}")

        # 创建构建上下文
        context = self.create_build_context(dockerfile)
        if not context:
            # 如果是服务器镜像，还原Dockerfile
            if image_type == "server":
                self.restore_dockerfile(dockerfile)
            return False

        # 尝试多次构建
        for attempt in range(1, self.retries + 1):
            try:
                # 配置更宽松的API超时
                api_timeout = self.timeout * 2  # 加倍超时时间

                # 准备构建参数
                build_args = self.build_args.copy()

                # 根据镜像类型添加不同的参数
                if image_type == "server":
                    # 添加pip镜像源参数（清华源）
                    build_args['PIP_INDEX_URL'] = self.pip_mirror
                    build_args['PIP_TIMEOUT'] = '300'
                    build_args['PIP_RETRIES'] = '5'

                self.print_info(f"使用构建参数: {build_args}")

                # 开始构建
                build_start = time.time()
                build_logs = self.client.api.build(
                    fileobj=context,
                    custom_context=True,
                    dockerfile=dockerfile,
                    tag=image_name,
                    rm=True,
                    decode=True,
                    buildargs=build_args,
                    timeout=api_timeout,
                    nocache=self.args.no_cache,  # 添加nocache选项
                    pull=True,  # 强制拉取基础镜像的最新版本
                    network_mode="host" if self.args.network_host else None  # 使用主机网络模式
                )

                # 处理构建日志
                for chunk in build_logs:
                    if 'stream' in chunk:
                        log_line = chunk['stream'].strip()
                        if log_line:
                            print(log_line)
                    elif 'error' in chunk:
                        self.print_error(chunk['error'].strip())
                        break

                # 验证构建是否成功
                try:
                    self.client.images.get(image_name)
                    build_time = time.time() - build_start
                    self.print_info(f"{image_type} 镜像构建成功: {image_name} (耗时: {build_time:.2f}s)")

                    # 如果是服务器镜像，还原Dockerfile
                    if image_type == "server":
                        self.restore_dockerfile(dockerfile)

                    return True
                except ImageNotFound:
                    raise Exception(f"构建似乎成功但找不到镜像: {image_name}")

            except (APIError, Exception) as e:
                if "net/http: request canceled" in str(e) or "timeout" in str(e).lower():
                    error_message = "网络连接超时，尝试使用 --network-host 选项或配置Docker镜像加速器"
                elif "could not find a version that satisfies the requirement" in str(e).lower():
                    error_message = "无法找到匹配的Python包版本，已尝试自动调整numpy版本"
                else:
                    error_message = str(e)

                if attempt < self.retries:
                    self.print_warn(f"构建尝试 {attempt}/{self.retries} 失败: {error_message}")
                    self.print_info(f"等待 {RETRY_DELAY} 秒后重试...")
                    # 重置context位置
                    context.seek(0)
                    time.sleep(RETRY_DELAY)
                else:
                    self.print_error(f"{image_type} 镜像构建失败，已尝试 {self.retries} 次: {error_message}")
                    context.close()

                    # 如果是服务器镜像，还原Dockerfile
                    if image_type == "server":
                        self.restore_dockerfile(dockerfile)

                    return False

        context.close()

        # 如果是服务器镜像，还原Dockerfile
        if image_type == "server":
            self.restore_dockerfile(dockerfile)

        return False

    def run(self):
        """执行主要流程"""
        # 选择服务器
        if not self.select_server():
            return False

        # 连接到Docker
        if not self.connect_to_docker():
            return False

        # 显示构建信息
        self.print_info("=== 构建配置 ===")
        self.print_info(f"镜像前缀: {self.image_prefix}")
        self.print_info(f"镜像标签: {self.image_tag}")

        if self.clean_only:
            self.print_info("模式: 仅清理旧镜像")
            if self.force_clean:
                self.print_info("将强制清理旧镜像，即使正在被使用")
        else:
            self.print_info(f"构建服务器镜像: {self.build_server}")
            if self.build_server:
                self.print_info(f"pip镜像源: {self.pip_mirror} (清华源)")
                self.print_info("注意: 构建前会临时修改Dockerfile添加pip镜像源，构建完成后会自动还原")

            self.print_info(f"构建Web界面镜像: {self.build_web}")
            if self.build_web and 'NPM_CONFIG_REGISTRY' in self.build_args:
                self.print_info(f"npm镜像源: {self.build_args.get('NPM_CONFIG_REGISTRY', '默认')}")

            self.print_info(f"最大重试次数: {self.retries}")
            self.print_info(f"连接超时: {self.timeout}秒")

            # 显示高级构建选项
            if self.args.network_host:
                self.print_info("网络模式: host（使用主机网络，可能解决连接问题）")
            if self.args.no_cache:
                self.print_info("已禁用缓存: 所有层将重新构建")

            # 显示清理选项
            if self.should_clean:
                self.print_info("构建成功后将自动清理旧镜像")
                if self.force_clean:
                    self.print_info("将强制清理旧镜像，即使正在被使用")

        # 确认构建
        if not self.args.yes:
            try:
                confirm = input("是否继续? [Y/n] ").strip().lower()
                if confirm and confirm not in ['y', 'yes']:
                    self.print_info("操作已取消")
                    return False
            except (KeyboardInterrupt, EOFError):
                self.print_info("操作已取消")
                return False

        # 只清理模式
        if self.clean_only:
            self.print_info("=== 清理旧镜像 ===")
            server_cleaned = self.clean_old_images("server", force=self.force_clean)
            web_cleaned = self.clean_old_images("web", force=self.force_clean)
            return server_cleaned and web_cleaned

        # 构建服务器镜像
        server_success = True
        if self.build_server:
            self.print_info("=== 构建服务器镜像 ===")
            server_success = self.build_image("server")

        # 构建Web界面镜像
        web_success = True
        if self.build_web:
            self.print_info("=== 构建Web界面镜像 ===")
            web_success = self.build_image("web")

        # 清理旧镜像
        if self.should_clean:
            if server_success and self.build_server:
                self.clean_old_images("server", force=self.force_clean)
            if web_success and self.build_web:
                self.clean_old_images("web", force=self.force_clean)

        # 总结
        self.print_info("=== 构建结果 ===")
        if self.build_server:
            status = f"{Fore.GREEN}成功{Style.RESET_ALL}" if server_success else f"{Fore.RED}失败{Style.RESET_ALL}"
            print(f"服务器镜像: {self.server_image} - {status}")

        if self.build_web:
            status = f"{Fore.GREEN}成功{Style.RESET_ALL}" if web_success else f"{Fore.RED}失败{Style.RESET_ALL}"
            print(f"Web界面镜像: {self.web_image} - {status}")

        return server_success and web_success

    def clean_old_images(self, image_type, force=False):
        """清理旧的Docker镜像

        Args:
            image_type: 镜像类型（server或web）
            force: 是否强制删除镜像（即使正在被使用）
        """
        try:
            if image_type == "server":
                # 查找服务器相关的旧镜像
                image_prefix = f"{self.image_prefix}:server_"
                current_image = self.server_image
            else:  # web
                # 查找web相关的旧镜像
                image_prefix = f"{self.image_prefix}:web_"
                current_image = self.web_image

            self.print_info(f"正在清理旧的 {image_type} 镜像...")

            # 列出所有镜像
            images = self.client.images.list()
            cleaned = 0
            skipped = 0
            failed = 0
            total_space_saved = 0  # 以字节为单位

            for image in images:
                # 检查是否有标签
                if not image.tags:
                    continue

                for tag in image.tags:
                    # 只处理当前类型的镜像，且不是当前构建的镜像
                    if tag.startswith(image_prefix) and tag != current_image:
                        try:
                            # 获取镜像大小
                            img_size = image.attrs.get('Size', 0)

                            self.print_info(f"正在删除旧镜像: {tag} (大小: {self._format_size(img_size)})")
                            self.client.images.remove(tag, force=force)
                            cleaned += 1
                            total_space_saved += img_size
                        except Exception as e:
                            if "image is being used" in str(e).lower():
                                # 容器正在使用此镜像
                                if force:
                                    try:
                                        self.print_warn(f"镜像 {tag} 正在被使用，强制删除...")
                                        self.client.images.remove(tag, force=True)
                                        cleaned += 1
                                        total_space_saved += image.attrs.get('Size', 0)
                                        self.print_info(f"已强制删除 {tag}")
                                    except Exception as e2:
                                        self.print_error(f"强制删除失败: {str(e2)}")
                                        failed += 1
                                else:
                                    self.print_warn(f"镜像 {tag} 正在被使用，跳过 (使用 --force-clean 强制删除)")
                                    skipped += 1
                            else:
                                # 其他错误
                                self.print_warn(f"无法删除镜像 {tag}: {str(e)}")
                                failed += 1

            # 总结清理结果
            if cleaned > 0:
                self.print_info(f"已成功清理 {cleaned} 个旧的 {image_type} 镜像，释放 {self._format_size(total_space_saved)} 空间")
                if skipped > 0:
                    self.print_warn(f"跳过 {skipped} 个正在使用的镜像")
                if failed > 0:
                    self.print_error(f"{failed} 个镜像删除失败")
            else:
                self.print_info(f"没有找到需要清理的旧 {image_type} 镜像")

            return True
        except Exception as e:
            self.print_warn(f"清理旧镜像失败: {str(e)}")
            return False

    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes/1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes/(1024*1024):.2f} MB"
        else:
            return f"{size_bytes/(1024*1024*1024):.2f} GB"

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="小智ESP32服务器远程Docker构建工具",
        epilog="示例: ./docker_remote_build.py --test --tag v1.0"
    )

    # 服务器选择
    server_group = parser.add_mutually_exclusive_group()
    server_group.add_argument("--prod", action="store_true", help="构建在正式环境服务器上")
    server_group.add_argument("--test", action="store_true", help="构建在测试环境服务器上")

    # 镜像配置
    parser.add_argument("-p", "--prefix", default=DEFAULT_IMAGE_PREFIX, help=f"镜像名称前缀 (默认: {DEFAULT_IMAGE_PREFIX})")
    parser.add_argument("-t", "--tag", default=DEFAULT_IMAGE_TAG, help=f"镜像标签 (默认: {DEFAULT_IMAGE_TAG})")

    # 构建类型
    build_group = parser.add_mutually_exclusive_group()
    build_group.add_argument("--server-only", action="store_true", help="仅构建服务器镜像")
    build_group.add_argument("--web-only", action="store_true", help="仅构建Web界面镜像")
    build_group.add_argument("--clean-only", action="store_true", help="仅清理旧镜像，不进行构建")

    # 镜像源配置
    parser.add_argument("--pip-mirror", help=f"指定pip镜像源URL (默认: {DEFAULT_PIP_MIRROR})")
    parser.add_argument("--npm-mirror", help="指定npm镜像源URL")

    # 网络和重试配置
    parser.add_argument("--timeout", type=int, default=180, help="Docker API调用超时时间(秒)")
    parser.add_argument("--retries", type=int, default=MAX_RETRIES, help=f"最大重试次数 (默认: {MAX_RETRIES})")
    parser.add_argument("--network-host", action="store_true", help="使用主机网络模式构建，可能解决网络问题")
    parser.add_argument("--no-cache", action="store_true", help="不使用缓存构建镜像")

    # 清理选项
    parser.add_argument("--clean", action="store_true", help="构建成功后清理旧镜像")
    parser.add_argument("--no-clean", action="store_true", help="构建成功后不清理旧镜像")
    parser.add_argument("--force-clean", action="store_true", help="强制清理旧镜像，即使正在被使用")

    # 其他选项
    parser.add_argument("-y", "--yes", action="store_true", help="自动确认所有提示")
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细日志")

    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 创建构建器
    builder = DockerRemoteBuilder(args)

    # 执行构建
    if builder.run():
        sys.exit(0)
    else:
        sys.exit(1)

def setup_docker_mirror(client):
    """设置Docker镜像加速器"""
    try:
        # 获取当前配置
        info = client.info()
        registry_mirrors = info.get('RegistryConfig', {}).get('Mirrors', [])

        if not registry_mirrors:
            print(f"{Fore.YELLOW}[WARN] 远程Docker守护进程未配置镜像加速器，可能导致拉取镜像超时{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}[WARN] 建议在远程服务器上配置以下镜像加速器之一:{Style.RESET_ALL}")
            for mirror in DOCKER_MIRRORS:
                print(f"{Fore.YELLOW}  - {mirror}{Style.RESET_ALL}")
        else:
            print(f"{Fore.GREEN}[INFO] 远程Docker守护进程已配置镜像加速器:{Style.RESET_ALL}")
            for mirror in registry_mirrors:
                print(f"{Fore.GREEN}  - {mirror}{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.YELLOW}[WARN] 无法检查Docker镜像加速器配置: {str(e)}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()