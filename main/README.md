本文档是开发类文档，如需部署小智服务端，[点击这里查看部署教程](../README.md#%E9%83%A8%E7%BD%B2%E6%96%87%E6%A1%A3)

# 项目目录介绍
如果你是一名软件开发者，这里有一份[《致开发者的公开信》](../docs/contributor_open_letter.md)，欢迎归队！

```
xiaozhi-esp32-server
  ├─ xiaozhi-server 8000 端口 Python语言开发 负责与esp32通信
  ├─ manager-web 8001 端口 Node.js+Vue开发 负责提供控制台的web界面
  ├─ manager-api 8002 端口 Java语言开发 负责提供控制台的api
```

# xiaozhi-server 和ESP32通讯协议

https://ccnphfhqs21z.feishu.cn/wiki/M0XiwldO9iJwHikpXD5cEx71nKh

# manager-web 、manager-api接口协议

https://2662r3426b.vicp.fun/xiaozhi/doc.html
