package xiaozhi.modules.agent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体-账号映射DTO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Data
@Schema(description = "智能体-账号映射")
public class AgentMappingDTO {
    
    @Schema(description = "智能体ID")
    private String id;
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "智能体名称")
    private String agentName;
    
    @Schema(description = "是否为官方默认智能体")
    private Boolean isDefault;
    
    @Schema(description = "智能体状态")
    private Integer status;
} 