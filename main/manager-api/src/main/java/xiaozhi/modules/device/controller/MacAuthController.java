package xiaozhi.modules.device.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.utils.Result;
import xiaozhi.modules.device.service.MacAddressService;
import xiaozhi.modules.quota.dao.MacBlacklistDao;

/**
 * MAC地址认证控制器
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@RestController
@RequestMapping("device/auth")
@Tag(name = "MAC地址认证")
@AllArgsConstructor
public class MacAuth<PERSON>ontroller {

    private final MacAddressService macAddressService;
    private final MacBlacklistDao macBlacklistDao;
    private final RedisUtils redisUtils;

    // 缓存过期时间（秒）
    private static final int CACHE_TTL = 300; // 5分钟

    @GetMapping("/check/{macAddress}")
    @Operation(summary = "检查MAC地址是否有效")
    public Result<Boolean> checkMacAddress(@PathVariable("macAddress") String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return new Result<Boolean>().ok(false);
        }

        // 检查MAC地址格式
        if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
            return new Result<Boolean>().ok(false);
        }

        // 尝试从Redis缓存获取结果
        String cacheKey = RedisKeys.getMacAuthKey(macAddress);
        Object cachedResult = redisUtils.get(cacheKey);

        if (cachedResult != null) {
            if ("blacklisted".equals(cachedResult)) {
                return new Result<Boolean>().error("MAC地址已被禁用");
            } else {
                return new Result<Boolean>().ok(Boolean.valueOf(cachedResult.toString()));
            }
        }

        // 缓存未命中，查询数据库

        // 检查是否在黑名单中
        if (macBlacklistDao.checkMacAddressInBlacklist(macAddress) > 0) {
            // 缓存黑名单结果
            redisUtils.set(cacheKey, "blacklisted", CACHE_TTL);
            return new Result<Boolean>().error("MAC地址已被禁用");
        }

        // 检查是否在白名单中且已启用
        boolean isEnabled = macAddressService.checkMacAddressEnabled(macAddress);

        // 缓存结果
        redisUtils.set(cacheKey, isEnabled, CACHE_TTL);

        return new Result<Boolean>().ok(isEnabled);
    }

    @GetMapping("/invalidate/{macAddress}")
    @Operation(summary = "使MAC地址缓存失效")
    public Result<Void> invalidateCache(@PathVariable("macAddress") String macAddress) {
        if (StringUtils.isBlank(macAddress)) {
            return new Result<Void>().error("MAC地址不能为空");
        }

        String cacheKey = RedisKeys.getMacAuthKey(macAddress);
        redisUtils.delete(cacheKey);

        return new Result<Void>().ok(null);
    }
}
