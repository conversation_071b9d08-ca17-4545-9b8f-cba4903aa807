package xiaozhi.modules.device.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import xiaozhi.modules.device.entity.MacAddressEntity;

import java.util.List;

/**
 * MAC地址白名单DAO
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Mapper
public interface MacAddressDao extends BaseMapper<MacAddressEntity> {

    /**
     * 获取所有启用的MAC地址列表
     *
     * @return MAC地址列表
     */
    @Select("SELECT mac_address FROM ai_mac_whitelist WHERE status = 1")
    List<String> getAllEnabledMacAddresses();

    /**
     * 分页获取启用的MAC地址列表
     *
     * @param offset 偏移量
     * @param limit 每页数量
     * @return MAC地址列表
     */
    @Select("SELECT mac_address FROM ai_mac_whitelist WHERE status = 1 LIMIT #{offset}, #{limit}")
    List<String> getEnabledMacAddressesByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 获取启用的MAC地址总数
     *
     * @return MAC地址总数
     */
    @Select("SELECT COUNT(*) FROM ai_mac_whitelist WHERE status = 1")
    int getEnabledMacAddressCount();

    /**
     * 检查MAC地址是否存在
     *
     * @param macAddress MAC地址
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) FROM ai_mac_whitelist WHERE mac_address = #{macAddress}")
    int checkMacAddressExists(String macAddress);

    /**
     * 检查MAC地址是否启用
     *
     * @param macAddress MAC地址
     * @return 是否启用
     */
    @Select("SELECT COUNT(*) FROM ai_mac_whitelist WHERE mac_address = #{macAddress} AND status = 1")
    int checkMacAddressEnabled(String macAddress);

    /**
     * 批量检查MAC地址是否启用
     *
     * @param macAddresses MAC地址列表
     * @return 启用的MAC地址列表
     */
    @Select("<script>SELECT mac_address FROM ai_mac_whitelist WHERE status = 1 AND mac_address IN " +
            "<foreach collection='macAddresses' item='mac' open='(' separator=',' close=')'>" +
            "#{mac}" +
            "</foreach></script>")
    List<String> batchCheckMacAddressesEnabled(@Param("macAddresses") List<String> macAddresses);
}
