package xiaozhi.modules.device.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.common.redis.RedisKeys;
import xiaozhi.common.redis.RedisUtils;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.common.utils.ConvertUtils;
import xiaozhi.modules.device.dao.MacAddressDao;
import xiaozhi.modules.device.dto.MacAddressDTO;
import xiaozhi.modules.device.entity.MacAddressEntity;
import xiaozhi.modules.device.service.MacAddressService;
import xiaozhi.modules.quota.dao.MacBlacklistDao;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * MAC地址白名单服务实现
 *
 * <AUTHOR>
 * @version 1.0, 2025/6/15
 * @since 1.0.0
 */
@Service
public class MacAddressServiceImpl extends BaseServiceImpl<MacAddressDao, MacAddressEntity> implements MacAddressService {

    private final MacBlacklistDao macBlacklistDao;
    private final RedisUtils redisUtils;

    // 缓存过期时间（秒）
    private static final int CACHE_TTL = 3600; // 1小时

    // 分页大小
    private static final int DEFAULT_PAGE_SIZE = 1000;

    public MacAddressServiceImpl(MacBlacklistDao macBlacklistDao, RedisUtils redisUtils) {
        this.macBlacklistDao = macBlacklistDao;
        this.redisUtils = redisUtils;
    }

    @Override
    public PageData<MacAddressDTO> page(Map<String, Object> params) {
        IPage<MacAddressEntity> page = baseDao.selectPage(
            getPage(params, Constant.CREATE_DATE, false),
            getWrapper(params)
        );

        return getPageData(page, MacAddressDTO.class);
    }

    private QueryWrapper<MacAddressEntity> getWrapper(Map<String, Object> params) {
        String macAddress = (String)params.get("macAddress");
        String remark = (String)params.get("remark");
        Integer status = (Integer)params.get("status");

        QueryWrapper<MacAddressEntity> wrapper = new QueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(macAddress), "mac_address", macAddress);
        wrapper.like(StringUtils.isNotBlank(remark), "remark", remark);
        wrapper.eq(status != null, "status", status);

        return wrapper;
    }

    @Override
    public MacAddressDTO get(Long id) {
        MacAddressEntity entity = baseDao.selectById(id);
        return ConvertUtils.sourceToTarget(entity, MacAddressDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(MacAddressDTO dto) {
        MacAddressEntity entity = ConvertUtils.sourceToTarget(dto, MacAddressEntity.class);
        entity.setCreateDate(new Date());
        insert(entity);

        // 使缓存失效
        invalidateCache(entity.getMacAddress());

        // 如果状态为启用，则添加到白名单缓存
        if (entity.getStatus() == 1) {
            redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), entity.getMacAddress());
        }

        // 使白名单总数缓存失效
        redisUtils.delete(RedisKeys.getMacWhitelistCountKey());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(MacAddressDTO dto) {
        // 获取原始实体
        MacAddressEntity oldEntity = baseDao.selectById(dto.getId());

        // 更新实体
        MacAddressEntity entity = ConvertUtils.sourceToTarget(dto, MacAddressEntity.class);
        entity.setUpdateDate(new Date());
        updateById(entity);

        // 使缓存失效
        invalidateCache(entity.getMacAddress());

        // 处理白名单缓存
        if (oldEntity != null) {
            // 如果状态从启用变为禁用
            if (oldEntity.getStatus() == 1 && entity.getStatus() == 0) {
                redisUtils.sRemove(RedisKeys.getMacWhitelistSetKey(), entity.getMacAddress());
            }
            // 如果状态从禁用变为启用
            else if (oldEntity.getStatus() == 0 && entity.getStatus() == 1) {
                redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), entity.getMacAddress());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long[] ids) {
        // 先获取MAC地址，以便后续使缓存失效
        List<MacAddressEntity> entities = baseDao.selectBatchIds(Arrays.asList(ids));

        // 删除记录
        baseDao.deleteBatchIds(Arrays.asList(ids));

        // 使缓存失效
        for (MacAddressEntity entity : entities) {
            invalidateCache(entity.getMacAddress());

            // 从白名单缓存中移除
            if (entity.getStatus() == 1) {
                redisUtils.sRemove(RedisKeys.getMacWhitelistSetKey(), entity.getMacAddress());
            }
        }

        // 使白名单总数缓存失效
        redisUtils.delete(RedisKeys.getMacWhitelistCountKey());
    }

    @Override
    public List<String> getAllEnabledMacAddresses() {
        // 先尝试从缓存获取
        Set<String> cachedMacAddresses = getEnabledMacAddressesFromCache();
        if (cachedMacAddresses != null && !cachedMacAddresses.isEmpty()) {
            return new ArrayList<>(cachedMacAddresses);
        }

        // 缓存未命中，从数据库获取
        List<String> macAddresses = baseDao.getAllEnabledMacAddresses();

        // 更新缓存
        if (macAddresses != null && !macAddresses.isEmpty()) {
            String[] macArray = macAddresses.toArray(new String[0]);
            redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), macArray);
            redisUtils.expire(RedisKeys.getMacWhitelistSetKey(), CACHE_TTL);
        }

        return macAddresses;
    }

    @Override
    public List<String> getEnabledMacAddressesByPage(int page, int pageSize) {
        if (pageSize <= 0) {
            pageSize = DEFAULT_PAGE_SIZE;
        }

        // 计算偏移量
        int offset = (page - 1) * pageSize;

        // 先尝试从缓存获取
        String cacheKey = RedisKeys.getMacWhitelistPageKey(page, pageSize);
        List<String> cachedMacAddresses = (List<String>) redisUtils.get(cacheKey);
        if (cachedMacAddresses != null) {
            return cachedMacAddresses;
        }

        // 缓存未命中，从数据库获取
        List<String> macAddresses = baseDao.getEnabledMacAddressesByPage(offset, pageSize);

        // 更新缓存
        if (macAddresses != null) {
            redisUtils.set(cacheKey, macAddresses, CACHE_TTL);
        }

        return macAddresses;
    }

    @Override
    public int getEnabledMacAddressCount() {
        // 先尝试从缓存获取
        String cacheKey = RedisKeys.getMacWhitelistCountKey();
        Object cachedCount = redisUtils.get(cacheKey);
        if (cachedCount != null) {
            return Integer.parseInt(cachedCount.toString());
        }

        // 缓存未命中，从数据库获取
        int count = baseDao.getEnabledMacAddressCount();

        // 更新缓存
        redisUtils.set(cacheKey, count, CACHE_TTL);

        return count;
    }

    @Override
    public List<String> getAllBlacklistMacAddresses() {
        // 先尝试从缓存获取
        Set<String> cachedMacAddresses = getBlacklistMacAddressesFromCache();
        if (cachedMacAddresses != null && !cachedMacAddresses.isEmpty()) {
            return new ArrayList<>(cachedMacAddresses);
        }

        // 缓存未命中，从数据库获取
        List<String> macAddresses = macBlacklistDao.getAllMacAddresses();

        // 更新缓存
        if (macAddresses != null && !macAddresses.isEmpty()) {
            String[] macArray = macAddresses.toArray(new String[0]);
            redisUtils.sAdd(RedisKeys.getMacBlacklistSetKey(), macArray);
            redisUtils.expire(RedisKeys.getMacBlacklistSetKey(), CACHE_TTL);
        }

        return macAddresses;
    }

    @Override
    public List<String> getBlacklistMacAddressesByPage(int page, int pageSize) {
        if (pageSize <= 0) {
            pageSize = DEFAULT_PAGE_SIZE;
        }

        // 计算偏移量
        int offset = (page - 1) * pageSize;

        // 先尝试从缓存获取
        String cacheKey = RedisKeys.getMacBlacklistPageKey(page, pageSize);
        List<String> cachedMacAddresses = (List<String>) redisUtils.get(cacheKey);
        if (cachedMacAddresses != null) {
            return cachedMacAddresses;
        }

        // 缓存未命中，从数据库获取
        List<String> macAddresses = macBlacklistDao.getMacAddressesByPage(offset, pageSize);

        // 更新缓存
        if (macAddresses != null) {
            redisUtils.set(cacheKey, macAddresses, CACHE_TTL);
        }

        return macAddresses;
    }

    @Override
    public int getBlacklistMacAddressCount() {
        // 先尝试从缓存获取
        String cacheKey = RedisKeys.getMacBlacklistCountKey();
        Object cachedCount = redisUtils.get(cacheKey);
        if (cachedCount != null) {
            return Integer.parseInt(cachedCount.toString());
        }

        // 缓存未命中，从数据库获取
        int count = macBlacklistDao.getMacAddressCount();

        // 更新缓存
        redisUtils.set(cacheKey, count, CACHE_TTL);

        return count;
    }

    @Override
    public boolean checkMacAddressExists(String macAddress) {
        return baseDao.checkMacAddressExists(macAddress) > 0;
    }

    @Override
    public boolean checkMacAddressEnabled(String macAddress) {
        // 先检查缓存是否已加载
        if (redisUtils.sSize(RedisKeys.getMacWhitelistSetKey()) == 0) {
            // 缓存未加载，加载缓存
            loadMacAddressesToCache();
        }

        // 检查缓存
        if (isMacAddressInCache(macAddress)) {
            return true;
        }

        // 缓存中不存在，再次查询数据库确认
        // 这是为了处理缓存加载后新增的MAC地址
        boolean isEnabled = baseDao.checkMacAddressEnabled(macAddress) > 0;

        // 如果启用，则添加到缓存
        if (isEnabled) {
            redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), macAddress);
        }

        return isEnabled;
    }

    @Override
    public List<String> batchCheckMacAddressesEnabled(List<String> macAddresses) {
        if (macAddresses == null || macAddresses.isEmpty()) {
            return new ArrayList<>();
        }

        // 先检查缓存
        Set<String> enabledMacAddresses = new HashSet<>();
        Set<String> uncheckedMacAddresses = new HashSet<>();

        for (String macAddress : macAddresses) {
            if (isMacAddressInCache(macAddress)) {
                enabledMacAddresses.add(macAddress);
            } else {
                uncheckedMacAddresses.add(macAddress);
            }
        }

        // 如果所有MAC地址都在缓存中，直接返回
        if (uncheckedMacAddresses.isEmpty()) {
            return new ArrayList<>(enabledMacAddresses);
        }

        // 查询数据库
        List<String> dbEnabledMacAddresses = baseDao.batchCheckMacAddressesEnabled(new ArrayList<>(uncheckedMacAddresses));

        // 更新缓存
        if (dbEnabledMacAddresses != null && !dbEnabledMacAddresses.isEmpty()) {
            String[] macArray = dbEnabledMacAddresses.toArray(new String[0]);
            redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), macArray);

            // 添加到结果集
            enabledMacAddresses.addAll(dbEnabledMacAddresses);
        }

        return new ArrayList<>(enabledMacAddresses);
    }

    @Override
    public List<String> batchCheckMacAddressesInBlacklist(List<String> macAddresses) {
        if (macAddresses == null || macAddresses.isEmpty()) {
            return new ArrayList<>();
        }

        // 先检查缓存
        Set<String> blacklistedMacAddresses = new HashSet<>();
        Set<String> uncheckedMacAddresses = new HashSet<>();

        for (String macAddress : macAddresses) {
            if (isMacAddressInBlacklistCache(macAddress)) {
                blacklistedMacAddresses.add(macAddress);
            } else {
                uncheckedMacAddresses.add(macAddress);
            }
        }

        // 如果所有MAC地址都在缓存中，直接返回
        if (uncheckedMacAddresses.isEmpty()) {
            return new ArrayList<>(blacklistedMacAddresses);
        }

        // 查询数据库
        List<String> dbBlacklistedMacAddresses = macBlacklistDao.batchCheckMacAddressesInBlacklist(new ArrayList<>(uncheckedMacAddresses));

        // 更新缓存
        if (dbBlacklistedMacAddresses != null && !dbBlacklistedMacAddresses.isEmpty()) {
            String[] macArray = dbBlacklistedMacAddresses.toArray(new String[0]);
            redisUtils.sAdd(RedisKeys.getMacBlacklistSetKey(), macArray);

            // 添加到结果集
            blacklistedMacAddresses.addAll(dbBlacklistedMacAddresses);
        }

        return new ArrayList<>(blacklistedMacAddresses);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchImport(List<String> macAddresses) {
        int successCount = 0;

        for (String macAddress : macAddresses) {
            if (StringUtils.isBlank(macAddress)) {
                continue;
            }

            // 检查MAC地址格式
            if (!macAddress.matches("^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$")) {
                continue;
            }

            // 检查是否已存在
            if (checkMacAddressExists(macAddress)) {
                continue;
            }

            // 保存MAC地址
            MacAddressEntity entity = new MacAddressEntity();
            entity.setMacAddress(macAddress);
            entity.setStatus(1);
            entity.setBound(0);
            entity.setCreateDate(new Date());

            try {
                insert(entity);
                // 使缓存失效
                invalidateCache(macAddress);
                // 添加到白名单缓存
                redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), macAddress);
                successCount++;
            } catch (Exception e) {
                // 忽略异常，继续处理下一个
            }
        }

        // 使白名单总数缓存失效
        redisUtils.delete(RedisKeys.getMacWhitelistCountKey());

        return successCount;
    }

    @Override
    public void invalidateCache(String macAddress) {
        if (StringUtils.isNotBlank(macAddress)) {
            String cacheKey = RedisKeys.getMacAuthKey(macAddress);
            redisUtils.delete(cacheKey);
        }
    }

    @Override
    public void invalidateAllCache() {
        // 删除所有MAC地址相关的缓存
        redisUtils.delete(RedisKeys.getMacWhitelistSetKey());
        redisUtils.delete(RedisKeys.getMacBlacklistSetKey());
        redisUtils.delete(RedisKeys.getMacWhitelistCountKey());
        redisUtils.delete(RedisKeys.getMacBlacklistCountKey());
        redisUtils.delete(RedisKeys.getMacAuthConfigKey());

        // 删除所有分页缓存
        // 注意：这里只是删除了常用的分页大小，如果有其他分页大小，可能需要额外处理
        for (int page = 1; page <= 10; page++) {
            redisUtils.delete(RedisKeys.getMacWhitelistPageKey(page, DEFAULT_PAGE_SIZE));
            redisUtils.delete(RedisKeys.getMacBlacklistPageKey(page, DEFAULT_PAGE_SIZE));
        }
    }

    @Override
    public void loadMacAddressesToCache() {
        // 检查缓存是否已存在
        if (redisUtils.sSize(RedisKeys.getMacWhitelistSetKey()) > 0 &&
            redisUtils.sSize(RedisKeys.getMacBlacklistSetKey()) > 0) {
            // 缓存已存在，刷新过期时间
            redisUtils.expire(RedisKeys.getMacWhitelistSetKey(), CACHE_TTL);
            redisUtils.expire(RedisKeys.getMacBlacklistSetKey(), CACHE_TTL);
            redisUtils.expire(RedisKeys.getMacWhitelistCountKey(), CACHE_TTL);
            redisUtils.expire(RedisKeys.getMacBlacklistCountKey(), CACHE_TTL);
            return;
        }

        // 加载白名单
        List<String> whitelist = baseDao.getAllEnabledMacAddresses();
        if (whitelist != null && !whitelist.isEmpty()) {
            // 批量添加到缓存，提高性能
            String[] macArray = whitelist.toArray(new String[0]);
            redisUtils.sAdd(RedisKeys.getMacWhitelistSetKey(), macArray);
            redisUtils.expire(RedisKeys.getMacWhitelistSetKey(), CACHE_TTL);
        }

        // 加载黑名单
        List<String> blacklist = macBlacklistDao.getAllMacAddresses();
        if (blacklist != null && !blacklist.isEmpty()) {
            // 批量添加到缓存，提高性能
            String[] macArray = blacklist.toArray(new String[0]);
            redisUtils.sAdd(RedisKeys.getMacBlacklistSetKey(), macArray);
            redisUtils.expire(RedisKeys.getMacBlacklistSetKey(), CACHE_TTL);
        }

        // 缓存总数
        redisUtils.set(RedisKeys.getMacWhitelistCountKey(), whitelist != null ? whitelist.size() : 0, CACHE_TTL);
        redisUtils.set(RedisKeys.getMacBlacklistCountKey(), blacklist != null ? blacklist.size() : 0, CACHE_TTL);
    }

    @Override
    public Set<String> getEnabledMacAddressesFromCache() {
        Set<Object> members = redisUtils.sMembers(RedisKeys.getMacWhitelistSetKey());
        if (members == null) {
            return null;
        }
        return members.stream()
                .filter(obj -> obj instanceof String)
                .map(obj -> (String) obj)
                .collect(java.util.stream.Collectors.toSet());
    }

    @Override
    public Set<String> getBlacklistMacAddressesFromCache() {
        Set<Object> members = redisUtils.sMembers(RedisKeys.getMacBlacklistSetKey());
        if (members == null) {
            return null;
        }
        return members.stream()
                .filter(obj -> obj instanceof String)
                .map(obj -> (String) obj)
                .collect(java.util.stream.Collectors.toSet());
    }

    @Override
    public boolean isMacAddressInCache(String macAddress) {
        return redisUtils.sIsMember(RedisKeys.getMacWhitelistSetKey(), macAddress);
    }

    @Override
    public boolean isMacAddressInBlacklistCache(String macAddress) {
        return redisUtils.sIsMember(RedisKeys.getMacBlacklistSetKey(), macAddress);
    }
}
