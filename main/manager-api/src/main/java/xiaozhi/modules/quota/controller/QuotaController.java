package xiaozhi.modules.quota.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.*;
import xiaozhi.common.annotation.LogOperation;
import xiaozhi.common.constant.Constant;
import xiaozhi.common.page.PageData;
import xiaozhi.common.user.UserDetail;
import xiaozhi.common.utils.Result;
import xiaozhi.common.validator.ValidatorUtils;
import xiaozhi.common.validator.group.AddGroup;
import xiaozhi.common.validator.group.DefaultGroup;
import xiaozhi.common.validator.group.UpdateGroup;
import xiaozhi.modules.quota.dto.QuotaSettingsDTO;
import xiaozhi.modules.quota.dto.QuotaUsageDTO;
import xiaozhi.modules.quota.service.QuotaService;
import xiaozhi.modules.security.user.SecurityUser;

import java.util.Map;

/**
 * 配额管理
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@RestController
@RequestMapping("quota")
@Tag(name = "配额管理")
@AllArgsConstructor
public class QuotaController {
    private final QuotaService quotaService;

    @GetMapping("settings")
    @Operation(summary = "获取配额设置")
    public Result<Map<String, Object>> getQuotaSettings() {
        Map<String, Object> settings = quotaService.getQuotaSettings();
        return new Result<Map<String, Object>>().ok(settings);
    }

    @PostMapping("settings")
    @Operation(summary = "更新配额设置")
    @LogOperation("更新配额设置")
    @RequiresPermissions("sys:params:update")
    public Result<Void> updateQuotaSettings(@RequestBody QuotaSettingsDTO dto) {
        // 效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        quotaService.updateQuotaSettings(dto);

        return new Result<>();
    }

    @GetMapping("page")
    @Operation(summary = "配额使用记录分页")
    @RequiresPermissions("sys:role:normal")
    public Result<PageData<QuotaUsageDTO>> page(@RequestParam Map<String, Object> params) {
        PageData<QuotaUsageDTO> page = quotaService.page(params);

        return new Result<PageData<QuotaUsageDTO>>().ok(page);
    }

    @GetMapping("user")
    @Operation(summary = "获取用户配额使用情况")
    @RequiresPermissions("sys:role:normal")
    public Result<Map<String, Object>> getUserQuotaUsage() {
        UserDetail user = SecurityUser.getUser();
        Map<String, Object> usage = quotaService.getUserQuotaUsage(user.getId());
        return new Result<Map<String, Object>>().ok(usage);
    }

    @GetMapping("device/{macAddress}")
    @Operation(summary = "获取设备配额使用情况")
    @RequiresPermissions("sys:role:normal")
    public Result<Map<String, Object>> getDeviceQuotaUsage(@PathVariable("macAddress") String macAddress) {
        Map<String, Object> usage = quotaService.getDeviceQuotaUsage(macAddress);
        return new Result<Map<String, Object>>().ok(usage);
    }

    @PostMapping("reset/{type}")
    @Operation(summary = "重置配额")
    @LogOperation("重置配额")
    @RequiresPermissions("sys:params:update")
    public Result<Void> resetQuota(@PathVariable("type") String type) {
        quotaService.resetQuota(type);
        return new Result<>();
    }
}
