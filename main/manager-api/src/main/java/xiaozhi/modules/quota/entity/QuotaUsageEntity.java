package xiaozhi.modules.quota.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 配额使用记录实体
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("ai_quota_usage")
public class QuotaUsageEntity {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户ID - 逻辑外键关联sys_user表的id字段
     */
    private Long userId;

    /**
     * 设备MAC地址 - 逻辑外键关联ai_device表的mac_address字段
     */
    private String deviceMac;

    /**
     * 智能体ID - 逻辑外键关联ai_agent表的id字段
     */
    private String agentId;

    /**
     * 使用类型(input/output)
     */
    private String usageType;

    /**
     * 使用量
     */
    private Integer usageValue;

    /**
     * 使用日期
     */
    private Date usageDate;

    /**
     * 创建时间
     */
    private Date createDate;
}
