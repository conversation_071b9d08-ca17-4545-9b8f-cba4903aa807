package xiaozhi.modules.quota.service;

import xiaozhi.common.page.PageData;
import xiaozhi.common.service.BaseService;
import xiaozhi.modules.quota.dto.QuotaSettingsDTO;
import xiaozhi.modules.quota.dto.QuotaUsageDTO;
import xiaozhi.modules.quota.dto.QuotaLimitDTO;
import xiaozhi.modules.quota.entity.QuotaSettingsEntity;

import java.util.Map;

/**
 * 配额服务接口
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
public interface QuotaService extends BaseService<QuotaSettingsEntity> {

    /**
     * 获取配额设置
     * 
     * @return 配额设置
     */
    Map<String, Object> getQuotaSettings();

    /**
     * 更新配额设置
     * 
     * @param dto 配额设置DTO
     */
    void updateQuotaSettings(QuotaSettingsDTO dto);

    /**
     * 分页查询配额使用记录
     * 
     * @param params 查询参数
     * @return 分页数据
     */
    PageData<QuotaUsageDTO> page(Map<String, Object> params);

    /**
     * 获取用户配额使用情况
     * 
     * @param userId 用户ID
     * @return 配额使用情况
     */
    Map<String, Object> getUserQuotaUsage(Long userId);

    /**
     * 获取设备配额使用情况
     * 
     * @param macAddress 设备MAC地址
     * @return 配额使用情况
     */
    Map<String, Object> getDeviceQuotaUsage(String macAddress);

    /**
     * 记录配额使用
     * 
     * @param userId 用户ID
     * @param deviceMac 设备MAC地址
     * @param agentId 智能体ID
     * @param usageType 使用类型
     * @param usageValue 使用量
     */
    void recordQuotaUsage(Long userId, String deviceMac, String agentId, String usageType, int usageValue);

    /**
     * 重置配额
     * 
     * @param type 重置类型（all/device/account）
     */
    void resetQuota(String type);

    /**
     * 检查设备配额限制
     * 
     * @param macAddress 设备MAC地址
     * @param agentId 智能体ID
     * @param requestedQuota 请求的配额量
     * @return 配额检查结果
     */
    QuotaLimitDTO checkDeviceQuotaLimit(String macAddress, String agentId, int requestedQuota);

    /**
     * 检查账号配额限制
     * 
     * @param userId 用户ID
     * @param requestedQuota 请求的配额量
     * @return 配额检查结果
     */
    QuotaLimitDTO checkAccountQuotaLimit(Long userId, int requestedQuota);

    /**
     * 获取账号每日总额度
     * 
     * @param userId 用户ID
     * @return 每日总额度
     */
    Integer getAccountDailyLimit(Long userId);

    /**
     * 获取设备每日限额（仅官方默认智能体）
     * 
     * @return 设备每日限额
     */
    Integer getDeviceDailyLimit();

    /**
     * 检查智能体是否为官方默认智能体
     * 
     * @param agentId 智能体ID
     * @return 是否为官方默认智能体
     */
    Boolean isDefaultAgent(String agentId);
}
