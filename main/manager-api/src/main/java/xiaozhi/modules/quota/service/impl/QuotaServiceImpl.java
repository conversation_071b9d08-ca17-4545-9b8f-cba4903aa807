package xiaozhi.modules.quota.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.agent.dao.AgentDao;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.device.dao.DeviceDao;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.quota.dao.QuotaSettingsDao;
import xiaozhi.modules.quota.dao.QuotaUsageDao;
import xiaozhi.modules.quota.dto.QuotaSettingsDTO;
import xiaozhi.modules.quota.dto.QuotaUsageDTO;
import xiaozhi.modules.quota.dto.QuotaLimitDTO;
import xiaozhi.modules.quota.entity.QuotaSettingsEntity;
import xiaozhi.modules.quota.entity.QuotaUsageEntity;
import xiaozhi.modules.quota.service.QuotaService;
import xiaozhi.modules.sys.dao.SysParamsDao;
import xiaozhi.modules.sys.dao.SysUserDao;
import xiaozhi.modules.sys.entity.SysUserEntity;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 配额服务实现
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Service
@AllArgsConstructor
public class QuotaServiceImpl extends BaseServiceImpl<QuotaSettingsDao, QuotaSettingsEntity> implements QuotaService {

    private final QuotaUsageDao quotaUsageDao;
    private final SysParamsDao sysParamsDao;
    private final DeviceDao deviceDao;
    private final AgentDao agentDao;
    private final SysUserDao sysUserDao;

    @Override
    public Map<String, Object> getQuotaSettings() {
        Map<String, Object> result = new HashMap<>();

        // 获取设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        if (deviceQuota != null) {
            result.put("deviceDefaultQuota", deviceQuota.getQuotaValue());
            result.put("deviceResetType", deviceQuota.getResetType());
        } else {
            result.put("deviceDefaultQuota", 10000);
            result.put("deviceResetType", "daily");
        }

        // 获取账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        if (accountQuota != null) {
            result.put("accountDefaultQuota", accountQuota.getQuotaValue());
            result.put("accountResetType", accountQuota.getResetType());
        } else {
            result.put("accountDefaultQuota", 100000);
            result.put("accountResetType", "daily");
        }

        // 获取是否启用配额限制
        String enableQuotaLimit = sysParamsDao.getValueByCode("enable_quota_limit");
        result.put("enableQuotaLimit", "true".equals(enableQuotaLimit));

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuotaSettings(QuotaSettingsDTO dto) {
        // 更新设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        if (deviceQuota == null) {
            deviceQuota = new QuotaSettingsEntity();
            deviceQuota.setQuotaType("device");
            deviceQuota.setDescription("设备每日配额");
            deviceQuota.setCreateDate(new Date());
        }
        deviceQuota.setQuotaValue(dto.getDeviceDefaultQuota());
        deviceQuota.setResetType(dto.getDeviceResetType());
        deviceQuota.setUpdateDate(new Date());
        if (deviceQuota.getId() == null) {
            insert(deviceQuota);
        } else {
            updateById(deviceQuota);
        }

        // 更新账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        if (accountQuota == null) {
            accountQuota = new QuotaSettingsEntity();
            accountQuota.setQuotaType("account");
            accountQuota.setDescription("账号每日配额");
            accountQuota.setCreateDate(new Date());
            insert(accountQuota);
        } else {
            accountQuota.setQuotaValue(dto.getAccountDefaultQuota());
            accountQuota.setResetType(dto.getAccountResetType());
            accountQuota.setUpdateDate(new Date());
            updateById(accountQuota);
        }

        // 更新是否启用配额限制
        sysParamsDao.updateValueByCode("enable_quota_limit", dto.getEnableQuotaLimit() ? "true" : "false");
    }

    @Override
    public PageData<QuotaUsageDTO> page(Map<String, Object> params) {
        // 创建正确类型的分页对象
        long curPage = 1;
        long limit = 10;

        if (params.get("page") != null) {
            curPage = Long.parseLong((String) params.get("page"));
        }
        if (params.get("limit") != null) {
            limit = Long.parseLong((String) params.get("limit"));
        }

        Page<QuotaUsageDTO> page = new Page<>(curPage, limit);

        // 调用DAO的分页方法
        IPage<QuotaUsageDTO> resultPage = quotaUsageDao.page(page, params);

        // 填充用户名、设备别名、智能体名称等信息
        for (QuotaUsageDTO dto : resultPage.getRecords()) {
            // 填充用户名
            if (dto.getUserId() != null) {
                SysUserEntity user = sysUserDao.selectById(dto.getUserId());
                if (user != null) {
                    dto.setUsername(user.getUsername());
                }
            }

            // 填充设备别名
            if (StringUtils.isNotBlank(dto.getDeviceMac())) {
                QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("mac_address", dto.getDeviceMac());
                DeviceEntity device = deviceDao.selectOne(wrapper);
                if (device != null) {
                    dto.setDeviceAlias(device.getAlias());
                }
            }

            // 填充智能体名称
            if (dto.getAgentId() != null) {
                AgentEntity agent = agentDao.selectById(dto.getAgentId());
                if (agent != null) {
                    dto.setAgentName(agent.getAgentName());
                }
            }
        }

        return new PageData<>(resultPage.getRecords(), resultPage.getTotal());
    }

    @Override
    public Map<String, Object> getUserQuotaUsage(Long userId) {
        Map<String, Object> result = new HashMap<>();

        // 获取账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        int quotaLimit = accountQuota != null ? accountQuota.getQuotaValue() : 100000;

        // 获取当日使用量
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getUserDailyUsage(userId, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }

        result.put("quotaLimit", quotaLimit);
        result.put("usedQuota", usedQuota);
        result.put("remainingQuota", quotaLimit > 0 ? quotaLimit - usedQuota : -1);

        return result;
    }

    @Override
    public Map<String, Object> getDeviceQuotaUsage(String macAddress) {
        Map<String, Object> result = new HashMap<>();

        // 获取设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        int quotaLimit = deviceQuota != null ? deviceQuota.getQuotaValue() : 10000;

        // 获取当日使用量
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getDeviceDailyUsage(macAddress, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }

        result.put("quotaLimit", quotaLimit);
        result.put("usedQuota", usedQuota);
        result.put("remainingQuota", quotaLimit > 0 ? quotaLimit - usedQuota : -1);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordQuotaUsage(Long userId, String deviceMac, String agentId, String usageType, int usageValue) {
        QuotaUsageEntity entity = new QuotaUsageEntity();
        entity.setUserId(userId);
        entity.setDeviceMac(deviceMac);
        entity.setAgentId(agentId);
        entity.setUsageType(usageType);
        entity.setUsageValue(usageValue);
        entity.setUsageDate(new Date());
        entity.setCreateDate(new Date());

        quotaUsageDao.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetQuota(String type) {
        QueryWrapper<QuotaUsageEntity> wrapper = new QueryWrapper<>();

        if ("all".equals(type)) {
            // 重置所有配额
            quotaUsageDao.delete(wrapper);
        } else if ("device".equals(type)) {
            // 重置设备配额
            wrapper.isNotNull("device_mac");
            quotaUsageDao.delete(wrapper);
        } else if ("account".equals(type)) {
            // 重置账号配额
            wrapper.isNotNull("user_id");
            quotaUsageDao.delete(wrapper);
        }
    }

    @Override
    public Boolean isDefaultAgent(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return false;
        }
        
        AgentEntity agent = agentDao.selectById(agentId);
        if (agent == null) {
            return false;
        }
        
        // 判断是否为官方默认智能体
        // 这里假设agentCode为"DEFAULT"或"OFFICIAL"的为默认智能体
        // 根据实际业务逻辑调整判断条件
        String agentCode = agent.getAgentCode();
        return StringUtils.isNotBlank(agentCode) && 
               ("DEFAULT".equalsIgnoreCase(agentCode) || "OFFICIAL".equalsIgnoreCase(agentCode));
    }

    @Override
    public QuotaLimitDTO checkDeviceQuotaLimit(String macAddress, String agentId, int requestedQuota) {
        QuotaLimitDTO result = new QuotaLimitDTO();
        result.setMacAddress(macAddress);
        result.setAgentId(agentId);
        result.setLimitType("device");
        
        // 检查是否启用配额限制
        String enableQuotaLimit = sysParamsDao.getValueByCode("enable_quota_limit");
        if (!"true".equals(enableQuotaLimit)) {
            // 未启用配额限制，返回无限额度
            result.setDeviceDailyLimit(-1);
            result.setRemainingQuota(-1);
            result.setUsedQuota(0);
            return result;
        }
        
        // 判断是否为默认智能体
        Boolean isDefault = isDefaultAgent(agentId);
        result.setIsDefault(isDefault);
        
        // 非默认智能体不受设备配额限制
        if (!isDefault) {
            result.setDeviceDailyLimit(-1);
            result.setRemainingQuota(-1);
            result.setUsedQuota(0);
            return result;
        }
        
        // 获取设备每日限额
        Integer deviceDailyLimit = getDeviceDailyLimit();
        result.setDeviceDailyLimit(deviceDailyLimit);
        
        // 获取当日已使用额度
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getDeviceDailyUsage(macAddress, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }
        result.setUsedQuota(usedQuota);
        
        // 计算剩余额度
        int remainingQuota = deviceDailyLimit > 0 ? deviceDailyLimit - usedQuota : -1;
        result.setRemainingQuota(remainingQuota);
        
        return result;
    }

    @Override
    public QuotaLimitDTO checkAccountQuotaLimit(Long userId, int requestedQuota) {
        QuotaLimitDTO result = new QuotaLimitDTO();
        result.setUserId(userId);
        result.setLimitType("account");
        
        // 检查是否启用配额限制
        String enableQuotaLimit = sysParamsDao.getValueByCode("enable_quota_limit");
        if (!"true".equals(enableQuotaLimit)) {
            // 未启用配额限制，返回无限额度
            result.setAccountDailyLimit(-1);
            result.setRemainingQuota(-1);
            result.setUsedQuota(0);
            return result;
        }
        
        // 获取账号每日限额
        Integer accountDailyLimit = getAccountDailyLimit(userId);
        result.setAccountDailyLimit(accountDailyLimit);
        
        // 获取当日已使用额度
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getUserDailyUsage(userId, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }
        result.setUsedQuota(usedQuota);
        
        // 计算剩余额度
        int remainingQuota = accountDailyLimit > 0 ? accountDailyLimit - usedQuota : -1;
        result.setRemainingQuota(remainingQuota);
        
        return result;
    }

    @Override
    public Integer getAccountDailyLimit(Long userId) {
        // 获取账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        int defaultLimit = accountQuota != null ? accountQuota.getQuotaValue() : 100000;
        
        // 这里可以根据用户ID查询特定用户的自定义配额
        // 如果没有特殊配置，则返回默认配额
        
        return defaultLimit;
    }

    @Override
    public Integer getDeviceDailyLimit() {
        // 获取设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        return deviceQuota != null ? deviceQuota.getQuotaValue() : 10000;
    }
}
