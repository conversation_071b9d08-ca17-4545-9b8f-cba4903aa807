from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from core.handle.iotHandle import send_iot_conn
import asyncio
import re

TAG = __name__
logger = setup_logging()

# 多语言支持字典
TRANSLATIONS = {
    "zh": {  # 中文
        "actions": {
            "forward": "前进",
            "backward": "后退",
            "stop": "停止",
            "left": "向左转",
            "right": "向右转",
            "dance": "跳舞",
            "spin": "旋转",
            "shake": "摇晃"
        },
        "speeds": {
            "slow": "缓慢地",
            "fast": "快速地"
        },
        "responses": {
            "stop": "好的，已停止移动。",
            "movement": "好的，正在{speed}{action}{duration}。",
            "error_action": "对不起，我不知道如何让设备执行'{action}'动作，我只能让设备前进、后退、停止、左转或右转。",
            "error_general": "对不起，控制设备{action}时出现问题: {error}",
            "complex_action": "好的，正在{action}。",
            "complex_action_duration": "好的，正在{action}，持续{duration}秒。",
            "distance_not_supported": "对不起，目前不支持精确的距离控制，我已将您的指令转为按时间控制。"
        },
        "units": {
            "seconds": "秒",
            "meters": "米",
            "centimeters": "厘米"
        }
    },
    "en": {  # 英文
        "actions": {
            "forward": "move forward",
            "backward": "move backward",
            "stop": "stop",
            "left": "turn left",
            "right": "turn right",
            "dance": "dance",
            "spin": "spin around",
            "shake": "shake"
        },
        "speeds": {
            "slow": "slowly ",
            "fast": "quickly "
        },
        "responses": {
            "stop": "Alright, movement stopped.",
            "movement": "OK, {speed}{action}{duration}.",
            "error_action": "Sorry, I don't know how to make the device '{action}'. I can only make it move forward, backward, stop, turn left or turn right.",
            "error_general": "Sorry, there was a problem controlling the device to {action}: {error}",
            "complex_action": "OK, {action} now.",
            "complex_action_duration": "OK, {action} for {duration} seconds.",
            "distance_not_supported": "Sorry, precise distance control is not supported yet. I've converted your instruction to time-based control."
        },
        "units": {
            "seconds": "seconds",
            "meters": "meters",
            "centimeters": "centimeters"
        }
    }
}

# 复杂动作定义（预设动作序列）
COMPLEX_ACTIONS = {
    "dance": [
        {"action": "spin", "speed": 70, "duration": 2},
        {"action": "forward", "speed": 60, "duration": 1},
        {"action": "backward", "speed": 60, "duration": 1},
        {"action": "left", "speed": 50, "duration": 1},
        {"action": "right", "speed": 50, "duration": 1},
        {"action": "spin", "speed": 70, "duration": 2}
    ],
    "spin": [
        {"action": "left", "speed": 80, "duration": 3}
    ],
    "shake": [
        {"action": "left", "speed": 60, "duration": 0.5},
        {"action": "right", "speed": 60, "duration": 0.5},
        {"action": "left", "speed": 60, "duration": 0.5},
        {"action": "right", "speed": 60, "duration": 0.5}
    ]
}

# 设备移动控制函数描述 - 多语言版本
handle_movement_function_desc = {
    "type": "function",
    "function": {
        "name": "handle_movement",
        "description": (
            "用户想要控制设备的移动，如前进、后退、停止、转向、跳舞等。支持中英文指令和时间/距离表达式。\n"
            "基本动作控制：\n"
            "- 例如用户说'向前走'或'move forward'，参数为：action:forward\n"
            "- 例如用户说'后退'或'go backward'，参数为：action:backward\n"
            "- 例如用户说'停下来'或'stop'，参数为：action:stop\n"
            "- 例如用户说'向左转'或'turn left'，参数为：action:left\n"
            "- 例如用户说'向右转'或'turn right'，参数为：action:right\n"
            "速度控制：\n"
            "- 例如用户说'以50%的速度前进'或'move forward at 50% speed'，参数为：action:forward,speed:50\n"
            "时间控制：\n"
            "- 例如用户说'前进10秒'或'move forward for 10 seconds'，参数为：action:forward,duration:10\n"
            "- 例如用户说'向左转5秒钟'或'turn left for 5 seconds'，参数为：action:left,duration:5\n"
            "距离表示（会自动转换为时间）：\n"
            "- 例如用户说'前进2米'或'move forward 2 meters'，参数为：action:forward,duration:4\n"
            "- 例如用户说'后退30厘米'或'move backward 30 centimeters'，参数为：action:backward,duration:1\n"
            "特殊动作：\n"
            "- 例如用户说'跳个舞'或'dance'，参数为：action:dance\n"
            "- 例如用户说'旋转'或'spin around'，参数为：action:spin\n"
            "- 例如用户说'摇一摇'或'shake'，参数为：action:shake\n"
        ),
        "parameters": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "移动动作/Movement action，基本值/basic options：forward(前进),backward(后退),stop(停止),left(向左转),right(向右转); 特殊动作/special actions: dance(跳舞),spin(旋转),shake(摇晃)"
                },
                "speed": {
                    "type": "integer",
                    "description": "移动速度/Movement speed，取值范围/range：0-100，默认为/default is 50"
                },
                "duration": {
                    "type": "integer",
                    "description": "移动持续时间(秒)/Duration(seconds)，默认为持续执行直到接收到stop命令/default is continuous until receiving stop command"
                },
                "distance": {
                    "type": "number",
                    "description": "移动距离(厘米)/Distance(centimeters)，系统会自动转换为对应的时间/will be converted to time automatically"
                },
                "language": {
                    "type": "string", 
                    "description": "用户使用的语言/User's language，可选值/options：zh(中文),en(英文)，默认根据用户输入自动检测/default is auto-detect"
                }
            },
            "required": ["action"]
        }
    }
}

def detect_language(text):
    """
    简单的语言检测函数，判断是中文还是英文
    基于输入文本中中文字符的比例来判断
    """
    if not text:
        return "zh"  # 默认中文
        
    # 如果是纯英文动作关键词，使用英文
    english_keywords = ["forward", "backward", "left", "right", "stop", "move", "turn", "go", "dance", "spin", "shake"]
    if text.lower() in english_keywords:
        return "en"
    
    # 检测中文字符的比例
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    if len(chinese_chars) > 0:
        return "zh"
    else:
        return "en"

def convert_distance_to_time(distance, action, speed=50):
    """
    将距离转换为时间（粗略估算）
    参数:
        distance: 距离，单位厘米
        action: 动作类型
        speed: 速度百分比
    返回:
        估算的时间（秒）
    """
    # 粗略估算：1米/秒的基准速度，受speed影响
    # 转向比直行慢
    base_speed = 50  # 厘米/秒，在speed=50%时
    
    if action in ["left", "right"]:
        # 转向通常是角度而非距离，这里返回一个合理的时间
        return max(1, int(distance / 90))  # 假设90°大约需要1秒
    
    # 根据速度调整
    adjusted_speed = base_speed * (speed / 50)
    
    # 计算时间
    time_seconds = distance / adjusted_speed
    
    # 确保最小时间为1秒
    return max(1, round(time_seconds))

async def _execute_movement(conn, action, speed=50, duration=None):
    """执行移动控制命令"""
    # Robot是设备类型名称，Move是方法名称
    device_type = "Robot"
    method_name = "Move"

    # 构建参数
    params = {
        "action": action,
        "speed": speed
    }
    
    if duration is not None:
        params["duration"] = duration
    
    # 发送控制命令
    await send_iot_conn(conn, device_type, method_name, params)
    
    # 如果指定了持续时间，等待指定时间后停止
    if duration is not None and duration > 0:
        await asyncio.sleep(duration)
        await send_iot_conn(conn, device_type, method_name, {"action": "stop", "speed": 0})
    
    return f"{action} at {speed}% speed" + (f" for {duration}s" if duration else "")

async def _execute_complex_action(conn, action_name, duration=None):
    """执行复杂动作序列"""
    if action_name not in COMPLEX_ACTIONS:
        return False
        
    actions = COMPLEX_ACTIONS[action_name]
    
    # 如果指定了持续时间，调整动作序列的总时长
    if duration is not None:
        # 计算原序列总时长
        original_total_duration = sum(action.get("duration", 1) for action in actions)
        
        # 计算时间比例
        time_ratio = duration / original_total_duration
        
        # 调整每个动作的时长
        for action in actions:
            if "duration" in action:
                action["duration"] = action["duration"] * time_ratio
    
    # 执行动作序列
    for step in actions:
        await _execute_movement(
            conn, 
            step.get("action", "stop"), 
            step.get("speed", 50), 
            step.get("duration", 1)
        )
    
    return True

@register_function('handle_movement', handle_movement_function_desc, ToolType.IOT_CTL)
def handle_movement(conn, action, speed=50, duration=None, distance=None, language=None):
    """处理设备移动控制（多语言支持、时间表达式、距离表达式、复杂动作）"""
    # 确定语言
    if language is None:
        # 根据用户消息自动检测语言
        try:
            last_message = conn.dialogue.dialogue[-1].content if conn.dialogue.dialogue else ""
            language = detect_language(last_message)
        except:
            language = "zh"  # 默认中文
    
    # 确保语言存在于翻译字典中
    if language not in TRANSLATIONS:
        language = "zh"  # 默认中文
        
    translations = TRANSLATIONS[language]
    
    # 验证基本动作
    basic_actions = ["forward", "backward", "stop", "left", "right"]
    complex_actions = list(COMPLEX_ACTIONS.keys())
    all_actions = basic_actions + complex_actions
    
    if action not in all_actions:
        return ActionResponse(
            action=Action.ERROR, 
            result=f"不支持的移动动作: {action}", 
            response=translations["responses"]["error_action"].format(action=action)
        )
    
    # 确保速度在有效范围内
    speed = max(0, min(100, int(speed)))
    
    # 如果提供了距离，转换为时间
    distance_info = ""
    if distance is not None and distance > 0:
        estimated_time = convert_distance_to_time(distance, action, speed)
        duration = estimated_time
        
        # 记录距离信息，用于日志和转换提示
        if language == "zh":
            distance_info = f"（距离{distance}{translations['units']['centimeters']}，转换为{duration}{translations['units']['seconds']}）"
        else:
            distance_info = f"(distance {distance} {translations['units']['centimeters']}, converted to {duration} {translations['units']['seconds']})"
    
    # 获取对应语言的动作描述
    action_desc = translations["actions"].get(action, action)
    
    try:
        result = ""
        # 处理复杂动作
        if action in complex_actions:
            # 执行复杂动作
            future = asyncio.run_coroutine_threadsafe(
                _execute_complex_action(conn, action, duration), conn.loop
            )
            future.result()  # 等待执行完成
            
            logger.bind(tag=TAG).info(f"复杂动作执行成功: {action}" + (f" for {duration}s" if duration else ""))
            
            # 构建响应消息
            if duration:
                response = translations["responses"]["complex_action_duration"].format(
                    action=action_desc,
                    duration=duration
                )
            else:
                response = translations["responses"]["complex_action"].format(
                    action=action_desc
                )
                
            if distance_info:
                response += " " + translations["responses"]["distance_not_supported"]
                
            return ActionResponse(action=Action.RESPONSE, result=action, response=response)
        
        # 处理基本动作
        future = asyncio.run_coroutine_threadsafe(
            _execute_movement(conn, action, speed, duration), conn.loop
        )
        
        result = future.result()
        logger.bind(tag=TAG).info(f"移动控制成功: {result}")
        
        # 构建响应消息
        if action == "stop":
            response = translations["responses"]["stop"]
        else:
            # 速度描述
            speed_desc = ""
            if speed < 30:
                speed_desc = translations["speeds"]["slow"]
            elif speed > 70:
                speed_desc = translations["speeds"]["fast"]
                
            # 持续时间描述
            if duration:
                if language == "zh":
                    duration_desc = f"，持续{duration}秒"
                else:
                    duration_desc = f" for {duration} seconds"
            else:
                duration_desc = ""
                
            # 格式化响应    
            response = translations["responses"]["movement"].format(
                speed=speed_desc,
                action=action_desc,
                duration=duration_desc
            )
            
            # 如果有距离转换信息，添加提示
            if distance_info:
                response += " " + translations["responses"]["distance_not_supported"]
            
        return ActionResponse(action=Action.RESPONSE, result=result, response=response)
    except Exception as e:
        logger.bind(tag=TAG).error(f"移动控制失败: {e}")
        response = translations["responses"]["error_general"].format(action=action_desc, error=str(e))
        return ActionResponse(action=Action.ERROR, result=None, response=response) 